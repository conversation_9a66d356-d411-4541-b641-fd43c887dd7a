<!DOCTYPE html>
<html>
<head>
    <title>Debug History Simple</title>
    <script src="static/js/search-history.js"></script>
</head>
<body>
    <h1>Debug History Simple</h1>
    <button onclick="test()">Clear & Add Test Recipe</button>
    <button onclick="goToHistory()">Go to History</button>
    <div id="output"></div>

    <script>
        function test() {
            console.log('Starting test...');
            
            // Clear history
            window.searchHistoryManager.clearHistory();
            console.log('History cleared');
            
            // Add a recipe with URL
            window.searchHistoryManager.addRecipeViewEntry(
                'Test Recipe with URL', 
                'home', 
                '/recipe/themealdb:53020/'
            );
            console.log('Recipe added');
            
            // Check what was saved
            const history = window.searchHistoryManager.getSearchHistory();
            console.log('History after adding:', history);
            
            if (history.length > 0) {
                const item = history[0];
                console.log('Item:', item);
                console.log('Filters:', item.filters);
                console.log('Recipe URL:', item.filters?.recipeUrl);
                
                document.getElementById('output').innerHTML = `
                    <h3>Saved Item:</h3>
                    <p>Query: ${item.query}</p>
                    <p>Type: ${item.searchType}</p>
                    <p>Recipe URL: ${item.filters?.recipeUrl || 'NOT FOUND'}</p>
                    <p>Full filters: ${JSON.stringify(item.filters)}</p>
                `;
            }
        }
        
        function goToHistory() {
            window.location.href = '/history/';
        }
    </script>
</body>
</html>
