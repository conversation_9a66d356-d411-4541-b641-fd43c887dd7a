<!-- Apple Dynamic Island-inspired Top Bar Component -->
<div 
    class="dynamic-island" 
    id="dynamic-island"
    x-data="{
        isExpanded: false,
        isHidden: false,
        isAuthenticated: false,
        appTitle: 'My Recipe Finder',
        favoritesCount: 0,
        historyCount: 0,
        init() {
            console.log('🏝️ Dynamic Island initialized');
            this.updateCounts();
            setTimeout(() => this.handleAutoExpansion(), 1000);
        },
        updateCounts() {
            if (window.searchHistoryManager) {
                this.historyCount = window.searchHistoryManager.getSearchHistory().length;
            }
            if (window.favoritesManager) {
                this.favoritesCount = window.favoritesManager.getFavoritesCount();
            }
        },
        handleAutoExpansion() {
            if (!sessionStorage.getItem('dynamic_island_auto_expanded')) {
                this.isExpanded = true;
                sessionStorage.setItem('dynamic_island_auto_expanded', 'true');
                setTimeout(() => this.isExpanded = false, 3000);
            }
        },
        expand() { this.isExpanded = true; this.updateCounts(); },
        collapse() { this.isExpanded = false; },
        handleFavoritesClick() {
            if (typeof showComingSoon === 'function') showComingSoon('Favorites');
        },
        handleHistoryClick() {
            if (window.searchHistoryDrawer?.openDrawer) {
                window.searchHistoryDrawer.openDrawer();
                this.collapse();
            } else {
                alert('History feature coming soon!');
            }
        }
    }"
    x-init="init()"
    :class="{ 
        'expanded': isExpanded, 
        'collapsed': !isExpanded,
        'hidden': isHidden 
    }"
    @click.away="autoCollapse()"
    role="banner"
    aria-label="Recipe Finder Navigation"
>
    <!-- Collapsed State -->
    <div 
        class="island-collapsed" 
        x-show="!isExpanded"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 scale-100"
        x-transition:leave-end="opacity-0 scale-95"
    >
        <button 
            class="island-trigger"
            @click="expand()"
            :aria-expanded="isExpanded"
            aria-controls="island-expanded-content"
            title="Expand Recipe Finder menu"
        >
            <span class="island-logo">🍳</span>
            <span class="island-title" x-text="appTitle"></span>
        </button>
    </div>

    <!-- Expanded State -->
    <div 
        class="island-expanded" 
        id="island-expanded-content"
        x-show="isExpanded"
        x-transition:enter="transition ease-out duration-500"
        x-transition:enter-start="opacity-0 scale-95 -translate-y-2"
        x-transition:enter-end="opacity-100 scale-100 translate-y-0"
        x-transition:leave="transition ease-in duration-300"
        x-transition:leave-start="opacity-100 scale-100 translate-y-0"
        x-transition:leave-end="opacity-0 scale-95 -translate-y-2"
        role="region"
        aria-label="Recipe Finder expanded menu"
    >
        <!-- Main Title Section -->
        <div class="island-header">
            <button 
                class="island-title-btn"
                @click="collapse()"
                :aria-expanded="isExpanded"
                aria-controls="island-expanded-content"
                title="Collapse menu"
            >
                <span class="island-logo">🍳</span>
                <span class="island-main-title" x-text="appTitle"></span>
                <svg class="island-collapse-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="18,15 12,9 6,15"/>
                </svg>
            </button>
        </div>

        <!-- Authentication Status -->
        <div class="island-auth-section" x-show="!isAuthenticated">
            <div class="auth-notification">
                <svg class="auth-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                    <circle cx="12" cy="7" r="4"/>
                </svg>
                <span class="auth-text">Sign in to sync</span>
                <div class="auth-badge">!</div>
            </div>
        </div>

        <!-- Action Icons Section -->
        <div class="island-actions">
            <!-- Favorites -->
            <button 
                class="island-action-btn"
                @click="handleFavoritesClick()"
                title="View favorites"
                aria-label="View favorites"
            >
                <div class="action-icon-container">
                    <svg class="action-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                    </svg>
                    <span class="action-count" x-show="favoritesCount > 0" x-text="favoritesCount"></span>
                </div>
                <span class="action-label">Favorites</span>
            </button>

            <!-- History -->
            <button 
                class="island-action-btn"
                @click="handleHistoryClick()"
                title="View history"
                aria-label="View history"
            >
                <div class="action-icon-container">
                    <svg class="action-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <polyline points="12,6 12,12 16,14"/>
                    </svg>
                    <span class="action-count" x-show="historyCount > 0" x-text="historyCount"></span>
                </div>
                <span class="action-label">History</span>
            </button>
        </div>
    </div>
</div>

<script>
// Initialize test data for demonstration
document.addEventListener('DOMContentLoaded', function() {
    // Add some test favorites for demonstration
    if (!localStorage.getItem('recipe_favorites')) {
        const testFavorites = [
            {
                id: 'test_1',
                recipeId: 'test_recipe_1',
                title: 'Chocolate Chip Cookies',
                source: 'test',
                timestamp: Date.now() - 86400000 // 1 day ago
            },
            {
                id: 'test_2',
                recipeId: 'test_recipe_2',
                title: 'Spaghetti Carbonara',
                source: 'test',
                timestamp: Date.now() - 172800000 // 2 days ago
            }
        ];
        localStorage.setItem('recipe_favorites', JSON.stringify(testFavorites));
        console.log('🧪 Test favorites added');
    }

    // Add some test search history if search history manager is available
    setTimeout(() => {
        if (window.searchHistoryManager && window.searchHistoryManager.getSearchHistory().length === 0) {
            window.searchHistoryManager.addSearchEntry('chicken pasta', 'ingredients', {}, 5);
            window.searchHistoryManager.addSearchEntry('chocolate dessert', 'ingredients', {}, 3);
            console.log('🧪 Test search history added');
        }
    }, 500);
});
</script>


