{% extends 'recipes/base.html' %}
{% load static %}

{% block title %}Recipe Finder - Search History{% endblock %}

{% block extra_css %}
<!-- All CSS is now consolidated in base.css, components.css, and pages.css -->
{% endblock %}

{% block content %}
<div class="history-container" x-data="historyPage()">
    <div class="history-header">
        <h1>Search History</h1>
        <p class="history-subtitle">Your recent recipe searches and discoveries</p>
    </div>



    <!-- Search History List -->
    <div class="history-content">
        <div class="history-actions">
            <button class="btn-clear-history" onclick="clearAllHistory()" title="Clear all search history">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Clear History
            </button>
            <button class="btn-export-history" onclick="exportHistory()" title="Export search history">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M7 10l5 5 5-5M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Export
            </button>
        </div>

        <div id="history-list" class="history-list">
            <!-- History items will be populated by JavaScript -->
        </div>

        <div id="no-history" class="no-history-message" style="display: none;">
            <div class="no-history-icon">🔍</div>
            <h3>No Search History</h3>
            <p>Your search history will appear here as you explore recipes.</p>
            <a href="{% url 'recipes:home' %}" class="btn-start-searching">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"/>
                    <path d="M21 21l-4.35-4.35"/>
                </svg>
                Start Searching
            </a>
        </div>
    </div>
</div>

<style>
/* History Page Styles */
.history-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.history-header {
    text-align: center;
    margin-bottom: 30px;
}

.history-header h1 {
    color: #FF6B35;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.history-subtitle {
    color: #6b7280;
    font-size: 1.1rem;
    margin: 0;
}



/* History Actions */
.history-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-bottom: 20px;
}

.btn-clear-history,
.btn-export-history {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: 1px solid #e5e7eb;
    background: white;
    border-radius: 8px;
    color: #6b7280;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-clear-history:hover {
    background: #fef2f2;
    border-color: #fca5a5;
    color: #dc2626;
}

.btn-export-history:hover {
    background: #fff3f0;
    border-color: #FF6B35;
    color: #FF6B35;
}

/* History List */
.history-list {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.history-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: all 0.2s ease;
}

.history-item:hover {
    background: #fff3f0;
    border-left: 4px solid #FF6B35;
    padding-left: 16px;
}

.history-item:last-child {
    border-bottom: none;
}

.history-item-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.history-item-query {
    font-weight: 600;
    color: #374151;
    font-size: 16px;
}

.history-item-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 13px;
    color: #6b7280;
}

.history-item-type {
    background: #f3f4f6;
    padding: 4px 10px;
    border-radius: 12px;
    font-weight: 500;
}

.history-item-type.ingredients { background: #dcfce7; color: #166534; }
.history-item-type.name { background: #dbeafe; color: #1e40af; }
.history-item-type.cuisine { background: #fef3c7; color: #92400e; }
.history-item-type.dietary { background: #f3e8ff; color: #7c3aed; }
.history-item-type.time { background: #fce7f3; color: #be185d; }

.history-item-results {
    color: #059669;
    font-weight: 500;
}

.history-item-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.history-item:hover .history-item-actions {
    opacity: 1;
}

.history-action-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.history-action-btn:hover {
    background: #f3f4f6;
    color: #FF6B35;
}

/* No History Message */
.no-history-message {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.no-history-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-history-message h3 {
    color: #374151;
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.no-history-message p {
    color: #6b7280;
    margin-bottom: 30px;
}

.btn-start-searching {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #FF6B35, #ff8c69);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.btn-start-searching:hover {
    background: linear-gradient(135deg, #e55a2b, #FF6B35);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
    color: white;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .history-container {
        padding: 15px;
    }
    
    .history-header h1 {
        font-size: 2rem;
    }
    

    
    .history-actions {
        flex-direction: column;
        gap: 8px;
    }
    
    .btn-clear-history,
    .btn-export-history {
        justify-content: center;
        width: 100%;
    }
    
    .history-item {
        padding: 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .history-item-actions {
        opacity: 1;
        align-self: flex-end;
    }
    
    .history-item-meta {
        flex-wrap: wrap;
    }
}
</style>

<script>
function historyPage() {
    return {
        init() {
            this.loadHistoryData();
        },
        
        loadHistoryData() {
            if (window.searchHistoryManager) {
                this.populateHistoryList();
            } else {
                // Wait for search history manager to load
                setTimeout(() => this.loadHistoryData(), 100);
            }
        },
        

        
        populateHistoryList() {
            const history = window.searchHistoryManager.getSearchHistory();
            const container = document.getElementById('history-list');
            const noHistoryMsg = document.getElementById('no-history');
            
            if (history.length === 0) {
                container.style.display = 'none';
                noHistoryMsg.style.display = 'block';
                return;
            }
            
            container.style.display = 'block';
            noHistoryMsg.style.display = 'none';
            
            container.innerHTML = history.map((item, index) => {
                // Debug: Log each history item
                console.log('History item:', item);
                console.log('Item filters:', item.filters);
                console.log('Recipe URL from filters:', item.filters?.recipeUrl);

                return `
                <div class="history-item" onclick="applyHistorySearchByIndex(${index})" data-index="${index}">
                    <div class="history-item-main">
                        <div class="history-item-query">${escapeHtml(item.query)}</div>
                        <div class="history-item-meta">
                            <span class="history-item-type ${item.searchType}">${item.searchType}</span>
                            <span class="history-item-time">${formatTimeAgo(item.timestamp)}</span>
                            ${item.resultCount > 0 ? `<span class="history-item-results">${item.resultCount} results</span>` : ''}
                        </div>
                    </div>
                    <div class="history-item-actions">
                        <button class="history-action-btn" onclick="event.stopPropagation(); removeHistoryItem('${item.id}')" title="Remove">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
                `;
            }).join('');
        }
    }
}

function applyHistorySearchByIndex(index) {
    // Get the current history and find the item by index
    const history = window.searchHistoryManager.getSearchHistory();
    const item = history[index];

    if (!item) {
        console.error('History item not found at index:', index);
        return;
    }

    const query = item.query;
    const searchType = item.searchType;
    const recipeUrl = item.filters?.recipeUrl;

    console.log('=== applyHistorySearchByIndex Debug ===');
    console.log('Index:', index);
    console.log('Item:', item);
    console.log('Query:', query);
    console.log('Search Type:', searchType);
    console.log('Recipe URL:', recipeUrl);
    console.log('Recipe URL type:', typeof recipeUrl);
    console.log('Recipe URL length:', recipeUrl ? recipeUrl.length : 'null/undefined');

    // Handle recipe views differently from search queries
    if (searchType === 'recipe_view') {
        // If we have a recipe URL, navigate directly to it
        if (recipeUrl && recipeUrl !== 'undefined' && recipeUrl.trim() !== '') {
            console.log('Recipe view clicked - direct navigation:', query, recipeUrl);
            window.location.href = recipeUrl;
        } else {
            // Fallback: search for the recipe by name
            console.log('Recipe view clicked - fallback search (no URL):', query);
            console.log('Reason: recipeUrl is empty, undefined, or "undefined"');
            const searchUrl = new URL('/', window.location.origin);
            searchUrl.searchParams.set('search_query', query);
            searchUrl.searchParams.set('search_type', 'recipe_name');
            window.location.href = searchUrl.toString();
        }
    } else {
        // For regular searches, navigate to search page with the query
        console.log('Regular search triggered');
        const searchUrl = new URL('/', window.location.origin);
        searchUrl.searchParams.set('search_query', query);
        searchUrl.searchParams.set('search_type', searchType);
        window.location.href = searchUrl.toString();
    }
}

// Keep the old function for backward compatibility
function applyHistorySearch(query, searchType, recipeUrl = '') {
    console.log('=== applyHistorySearch Debug (legacy) ===');
    console.log('Query:', query);
    console.log('Search Type:', searchType);
    console.log('Recipe URL:', recipeUrl);
    console.log('Recipe URL type:', typeof recipeUrl);
    console.log('Recipe URL length:', recipeUrl ? recipeUrl.length : 'null/undefined');

    // Handle recipe views differently from search queries
    if (searchType === 'recipe_view') {
        // If we have a recipe URL, navigate directly to it
        if (recipeUrl && recipeUrl !== 'undefined' && recipeUrl.trim() !== '') {
            console.log('Recipe view clicked - direct navigation:', query, recipeUrl);
            window.location.href = recipeUrl;
        } else {
            // Fallback: search for the recipe by name
            console.log('Recipe view clicked - fallback search (no URL):', query);
            console.log('Reason: recipeUrl is empty, undefined, or "undefined"');
            const searchUrl = new URL('/', window.location.origin);
            searchUrl.searchParams.set('search_query', query);
            searchUrl.searchParams.set('search_type', 'recipe_name');
            window.location.href = searchUrl.toString();
        }
    } else {
        // For regular searches, navigate to search page with the query
        console.log('Regular search triggered');
        const searchUrl = new URL('/', window.location.origin);
        searchUrl.searchParams.set('search_query', query);
        searchUrl.searchParams.set('search_type', searchType);
        window.location.href = searchUrl.toString();
    }
}

function removeHistoryItem(id) {
    if (window.searchHistoryManager) {
        window.searchHistoryManager.removeSearchEntry(id);
        // Reload the page data
        const app = document.querySelector('[x-data="historyPage()"]');
        if (app && app._x_dataStack) {
            app._x_dataStack[0].loadHistoryData();
        }
    }
}

function clearAllHistory() {
    if (confirm('Are you sure you want to clear all search history? This action cannot be undone.')) {
        if (window.searchHistoryManager) {
            window.searchHistoryManager.clearHistory();
            // Reload the page data
            const app = document.querySelector('[x-data="historyPage()"]');
            if (app && app._x_dataStack) {
                app._x_dataStack[0].loadHistoryData();
            }
        }
    }
}

function exportHistory() {
    if (window.searchHistoryManager) {
        const historyData = window.searchHistoryManager.exportHistory();
        const blob = new Blob([historyData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `recipe-search-history-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

function formatTimeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return new Date(timestamp).toLocaleDateString();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>

<!-- Search history is included in base.html -->
{% endblock %}
