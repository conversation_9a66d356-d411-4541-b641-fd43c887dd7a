/**
 * Favorites Manager for Recipe Finder
 * Manages user's favorite recipes with localStorage persistence
 * Integrates with Dynamic Island component for count display
 */

class FavoritesManager {
    constructor() {
        this.storageKey = 'recipe_favorites';
        this.maxFavorites = 100; // Reasonable limit
        this.init();
    }

    init() {
        console.log('⭐ Favorites Manager initialized');
        this.bindEvents();
    }

    /**
     * Get all favorites from localStorage
     */
    getFavorites() {
        try {
            const favorites = localStorage.getItem(this.storageKey);
            return favorites ? JSON.parse(favorites) : [];
        } catch (error) {
            console.error('Error loading favorites:', error);
            return [];
        }
    }

    /**
     * Add a recipe to favorites
     */
    addFavorite(recipe) {
        try {
            const favoriteEntry = {
                id: this.generateId(),
                recipeId: recipe.id || recipe.recipeId,
                title: recipe.title || recipe.name,
                image: recipe.image || recipe.imageUrl,
                source: recipe.source || 'unknown',
                url: recipe.url || recipe.recipeUrl,
                timestamp: Date.now(),
                date: new Date().toISOString()
            };

            let favorites = this.getFavorites();
            
            // Check if already favorited
            const existingIndex = favorites.findIndex(fav => 
                fav.recipeId === favoriteEntry.recipeId && fav.source === favoriteEntry.source
            );

            if (existingIndex !== -1) {
                console.log('Recipe already in favorites');
                return false;
            }

            // Add new favorite at the beginning
            favorites.unshift(favoriteEntry);

            // Limit the number of stored favorites
            if (favorites.length > this.maxFavorites) {
                favorites = favorites.slice(0, this.maxFavorites);
            }

            localStorage.setItem(this.storageKey, JSON.stringify(favorites));
            console.log('⭐ Recipe added to favorites:', favoriteEntry);

            // Dispatch event for UI updates
            this.dispatchFavoritesUpdated();
            return true;

        } catch (error) {
            console.error('Error adding favorite:', error);
            return false;
        }
    }

    /**
     * Remove a recipe from favorites
     */
    removeFavorite(recipeId, source = null) {
        try {
            let favorites = this.getFavorites();
            
            const initialLength = favorites.length;
            favorites = favorites.filter(fav => {
                if (source) {
                    return !(fav.recipeId === recipeId && fav.source === source);
                } else {
                    return fav.recipeId !== recipeId;
                }
            });

            if (favorites.length < initialLength) {
                localStorage.setItem(this.storageKey, JSON.stringify(favorites));
                console.log('⭐ Recipe removed from favorites:', recipeId);
                
                // Dispatch event for UI updates
                this.dispatchFavoritesUpdated();
                return true;
            }

            return false;

        } catch (error) {
            console.error('Error removing favorite:', error);
            return false;
        }
    }

    /**
     * Check if a recipe is favorited
     */
    isFavorited(recipeId, source = null) {
        try {
            const favorites = this.getFavorites();
            return favorites.some(fav => {
                if (source) {
                    return fav.recipeId === recipeId && fav.source === source;
                } else {
                    return fav.recipeId === recipeId;
                }
            });
        } catch (error) {
            console.error('Error checking favorite status:', error);
            return false;
        }
    }

    /**
     * Get favorites count
     */
    getFavoritesCount() {
        return this.getFavorites().length;
    }

    /**
     * Clear all favorites
     */
    clearFavorites() {
        try {
            localStorage.removeItem(this.storageKey);
            console.log('⭐ All favorites cleared');
            this.dispatchFavoritesUpdated();
            return true;
        } catch (error) {
            console.error('Error clearing favorites:', error);
            return false;
        }
    }

    /**
     * Get favorites statistics
     */
    getFavoritesStats() {
        const favorites = this.getFavorites();
        const now = Date.now();
        const oneWeek = 7 * 24 * 60 * 60 * 1000;

        // Count by source
        const sources = {};
        favorites.forEach(fav => {
            sources[fav.source] = (sources[fav.source] || 0) + 1;
        });

        // Recent favorites (last week)
        const recentFavorites = favorites.filter(fav => 
            (now - fav.timestamp) <= oneWeek
        );

        return {
            totalFavorites: favorites.length,
            recentFavorites: recentFavorites.length,
            sources: sources,
            oldestFavorite: favorites.length > 0 ? favorites[favorites.length - 1].date : null,
            newestFavorite: favorites.length > 0 ? favorites[0].date : null
        };
    }

    /**
     * Generate unique ID for favorites
     */
    generateId() {
        return 'fav_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Dispatch favorites updated event
     */
    dispatchFavoritesUpdated() {
        const event = new CustomEvent('favoritesUpdated', {
            detail: {
                count: this.getFavoritesCount(),
                favorites: this.getFavorites()
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Listen for recipe favorite/unfavorite actions
        document.addEventListener('click', (e) => {
            const favoriteBtn = e.target.closest('[data-favorite-action]');
            if (favoriteBtn) {
                this.handleFavoriteClick(favoriteBtn, e);
            }
        });
    }

    /**
     * Handle favorite button clicks
     */
    handleFavoriteClick(button, event) {
        event.preventDefault();
        event.stopPropagation();

        const action = button.dataset.favoriteAction;
        const recipeId = button.dataset.recipeId;
        const recipeTitle = button.dataset.recipeTitle;
        const recipeImage = button.dataset.recipeImage;
        const recipeSource = button.dataset.recipeSource;
        const recipeUrl = button.dataset.recipeUrl;

        if (!recipeId) {
            console.error('No recipe ID provided for favorite action');
            return;
        }

        const recipe = {
            id: recipeId,
            title: recipeTitle,
            image: recipeImage,
            source: recipeSource,
            url: recipeUrl
        };

        if (action === 'add') {
            if (this.addFavorite(recipe)) {
                this.updateFavoriteButton(button, 'remove');
                this.showFeedback('Added to favorites!', 'success');
            } else {
                this.showFeedback('Already in favorites', 'info');
            }
        } else if (action === 'remove') {
            if (this.removeFavorite(recipeId, recipeSource)) {
                this.updateFavoriteButton(button, 'add');
                this.showFeedback('Removed from favorites', 'success');
            }
        }
    }

    /**
     * Update favorite button state
     */
    updateFavoriteButton(button, newAction) {
        button.dataset.favoriteAction = newAction;
        
        const icon = button.querySelector('svg');
        if (icon) {
            if (newAction === 'remove') {
                // Filled star
                icon.innerHTML = '<polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26" fill="currentColor"/>';
                button.classList.add('favorited');
            } else {
                // Empty star
                icon.innerHTML = '<polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>';
                button.classList.remove('favorited');
            }
        }
    }

    /**
     * Show user feedback
     */
    showFeedback(message, type = 'info') {
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.className = `favorite-toast favorite-toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#4CAF50' : '#2196F3'};
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            z-index: 1001;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 100);

        // Remove after delay
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 2000);
    }
}

// Initialize favorites manager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (!window.favoritesManager) {
        window.favoritesManager = new FavoritesManager();
    }
});

// Make it globally available
window.FavoritesManager = FavoritesManager;
