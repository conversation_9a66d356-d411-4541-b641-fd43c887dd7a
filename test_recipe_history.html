<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Recipe History</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 10px; padding: 10px 20px; }
        .log { background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Test Recipe History</h1>
    
    <button onclick="clearAndAddTestData()">Clear History & Add Test Recipe</button>
    <button onclick="goToHistoryPage()">Go to History Page</button>
    <button onclick="testDirectNavigation()">Test Direct Navigation</button>
    
    <div id="log" class="log"></div>

    <!-- Include the search history manager -->
    <script src="static/js/search-history.js"></script>
    
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.log(message);
        }

        function clearAndAddTestData() {
            log('Clearing existing history...');
            window.searchHistoryManager.clearHistory();
            
            log('Adding test recipe with URL...');
            window.searchHistoryManager.addRecipeViewEntry(
                'Test Polish Soup', 
                'home', 
                '/recipe/themealdb:53020/'
            );
            
            log('Test data added. Check localStorage:');
            const history = window.searchHistoryManager.getSearchHistory();
            log('History items: ' + history.length);
            
            if (history.length > 0) {
                const item = history[0];
                log('First item query: ' + item.query);
                log('First item searchType: ' + item.searchType);
                log('First item filters: ' + JSON.stringify(item.filters));
                log('First item recipeUrl: ' + (item.filters?.recipeUrl || 'NOT FOUND'));
            }
        }

        function goToHistoryPage() {
            log('Navigating to history page...');
            window.location.href = '/history/';
        }

        function testDirectNavigation() {
            log('Testing direct navigation to recipe...');
            window.location.href = '/recipe/themealdb:53020/';
        }

        // Initialize
        window.addEventListener('load', function() {
            log('Page loaded. Search History Manager available: ' + !!window.searchHistoryManager);
        });
    </script>
</body>
</html>
